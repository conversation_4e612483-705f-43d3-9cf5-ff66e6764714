import { Sequelize } from "sequelize";
import { DataTypes, IModelOptions, IColumn } from "../types";

const sequelize = new Sequelize({});

const User = sequelize.define(
  "User",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      onDelete: "CASCADE",
    },
  },
  {
    sequelize,
    tableName: "User",
  }
);

User.belongsTo;

sequelize.createSchema();

export class Model {
  private readonly entityName: string;
  private readonly attributes: Record<string, IColumn>;
  private readonly options: IModelOptions;

  constructor(
    entityName: string,
    attributes: Record<string, IColumn>,
    options?: IModelOptions
  ) {
    this.entityName = entityName;
    this.attributes = attributes;
    this.options = options || {};
  }
}
